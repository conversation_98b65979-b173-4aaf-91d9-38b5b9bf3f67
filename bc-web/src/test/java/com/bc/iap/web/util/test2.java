package com.bc.iap.web.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
public class test2 {

    private final JdbcTemplate jdbcTemplate;

    @Test
    public void test() {
        updateShortDramasFromJson();
    }

    public void updateShortDramasFromJson() {
        try {
            // 读取JSON文件
            List<Map<String, Object>> dramaList = readJsonFile();

            int updatedCount = 0;
            int skippedCount = 0;

            for (Map<String, Object> drama : dramaList) {
                String id = (String) drama.get("id");
                String title = (String) drama.get("title");
                String description = (String) drama.get("description");
                Map<String, Integer>  likes =  (Map<String, Integer>) drama.get("likes");
                Map<String, Integer>  views =  (Map<String, Integer>) drama.get("views");

                // 获取rating值
                BigDecimal rating = null;
                Integer likesCount = null;
                Integer viewsCount = null;
                Map<String, Object> statistics = (Map<String, Object>) drama.get("statistics");
                if (statistics != null && statistics.get("rating") != null) {
                    Object ratingObj = statistics.get("rating");
                    if (ratingObj instanceof Number) {
                        rating = new BigDecimal(ratingObj.toString());
                    }
                    likesCount = likes.get("count");
                    viewsCount = views.get("count");
                }

                // 检查数据库中是否存在该记录，并且description或rating为空
                String checkSql = "SELECT COUNT(*) FROM short_dramas_copy1 WHERE md5_id = ?";
                Integer count = JdbcTemplate.queryForObject(checkSql, Integer.class, id);

                if (count != null && count > 0) {
                    // 更新记录
                    String updateSql = "UPDATE short_dramas_copy1 SET likes_count = COALESCE(likesCount, ?), views_count = COALESCE(likesCount, ?) WHERE md5_id = ?";
                    int rowsAffected = jdbcTemplate.update(updateSql, rating, id);

                    if (rowsAffected > 0) {
                        updatedCount++;
                    }
                } else {
                    skippedCount++;
                }
            }

        } catch (Exception e) {
            throw new RuntimeException("更新数据失败", e);
        }
    }

    /**
     * 读取JSON文件内容
     */
    private List<Map<String, Object>> readJsonFile() throws IOException {
        ClassPathResource resource = new ClassPathResource("test/json.json");
        try (InputStream inputStream = resource.getInputStream()) {
            ObjectMapper mapper = new ObjectMapper();
            TypeReference<List<Map<String, Object>>> typeRef = new TypeReference<List<Map<String, Object>>>() {};
            return mapper.readValue(inputStream, typeRef);
        }
    }
}
