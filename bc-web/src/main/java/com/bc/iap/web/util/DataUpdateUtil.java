package com.bc.iap.web.util;

import com.bc.iap.web.service.DataUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;

/**
 * 数据更新工具类
 * 可以通过命令行运行来更新数据库中的缺失字段
 */
@Slf4j
@SpringBootApplication
@ComponentScan(basePackages = "com.bc.iap.web")
public class DataUpdateUtil implements CommandLineRunner {

    @Autowired
    private DataUpdateService dataUpdateService;

    public static void main(String[] args) {
        log.info("开始执行数据更新工具...");
        ConfigurableApplicationContext context = SpringApplication.run(DataUpdateUtil.class, args);
        context.close();
        log.info("数据更新工具执行完成");
    }

    @Override
    public void run(String... args) throws Exception {
        try {
            log.info("=== 开始从JSON文件更新short_dramas_copy1表 ===");
            dataUpdateService.updateShortDramasFromJson();
            log.info("=== 数据更新完成 ===");
        } catch (Exception e) {
            log.error("数据更新失败", e);
            System.exit(1);
        }
    }
}
