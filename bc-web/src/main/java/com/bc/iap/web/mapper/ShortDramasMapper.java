package com.bc.iap.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bc.iap.web.common.vo.EpisodesDetailVo;
import com.bc.iap.web.common.vo.EpisodesVo;
import com.bc.iap.web.common.vo.HomepageVoCategoryName;
import com.bc.iap.web.entity.ShortDramas;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 短剧主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */

public interface ShortDramasMapper extends BaseMapper<ShortDramas> {
    @Select("SELECT short_dramas.title, episodes.drama_id, short_dramas.thumbnail_url, short_dramas.description,short_dramas.rating, short_dramas.views_count, short_dramas.likes_count, COUNT(episodes.drama_id) AS total_count " +
            "FROM short_dramas " +
            "INNER JOIN episodes " +
            "ON short_dramas.drama_id = episodes.drama_id " +
            "WHERE short_dramas.md5_id = #{md5Id} "
    )
    EpisodesVo selectByMd5Id(@Param("md5Id") String dramaId);

    @Select("SELECT " +
            "    short_dramas.md5_id, " +
            "    episodes.episode_id, " +
            "    episodes.drama_id, " +
            "    episodes.episode_number, " +
            "    episodes.title, " +
            "    episodes.release_date, " +
            "    episodes.duration, " +
            "    short_dramas.thumbnail_url, " +
            "    episodes.video_url, " +
            "    short_dramas.views_count, " +
            "    short_dramas.likes_count, " +
            "    episodes.word_url, " +
            "    short_dramas.description, " +
            "    (SELECT COUNT(*) FROM episodes WHERE drama_id = #{dramaId}) AS total_count " +
            "FROM episodes " +
            "INNER JOIN short_dramas " +
            "    ON short_dramas.drama_id = episodes.drama_id " +
            "WHERE episodes.drama_id = #{dramaId} " +
            "    AND episodes.episode_number = #{index}"
    )
    EpisodesDetailVo selectDetailByDramaId(@Param("dramaId") String dramaId, @Param("index") Integer index);

    @Select("SELECT short_dramas.*, categories.`name`" +
            "FROM short_dramas " +
            "INNER JOIN drama_categories " +
            "ON short_dramas.drama_id = drama_categories.drama_id " +
            "INNER JOIN categories " +
            "ON drama_categories.category_id = categories.category_id "
    )
    List<HomepageVoCategoryName> homepage();
}
