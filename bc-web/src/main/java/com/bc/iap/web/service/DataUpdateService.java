package com.bc.iap.web.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 数据更新服务类
 * 用于从JSON文件读取数据并更新数据库中缺失的字段
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataUpdateService {

    private final JdbcTemplate jdbcTemplate;
    private final ObjectMapper objectMapper;

    /**
     * 从JSON文件更新short_dramas_copy1表中缺失的description和rating字段
     */
    @Transactional
    public void updateShortDramasFromJson() {
        try {
            // 读取JSON文件
            List<Map<String, Object>> dramaList = readJsonFile();
            log.info("从JSON文件读取到{}条短剧数据", dramaList.size());

            int updatedCount = 0;
            int skippedCount = 0;

            for (Map<String, Object> drama : dramaList) {
                String id = (String) drama.get("id");
                String title = (String) drama.get("title");
                String description = (String) drama.get("description");
                
                // 获取rating值
                BigDecimal rating = null;
                Map<String, Object> statistics = (Map<String, Object>) drama.get("statistics");
                if (statistics != null && statistics.get("rating") != null) {
                    Object ratingObj = statistics.get("rating");
                    if (ratingObj instanceof Number) {
                        rating = new BigDecimal(ratingObj.toString());
                    }
                }

                // 检查数据库中是否存在该记录，并且description或rating为空
                String checkSql = "SELECT COUNT(*) FROM short_dramas_copy1 WHERE md5_id = ? AND (description IS NULL OR description = '' OR rating IS NULL)";
                Integer count = jdbcTemplate.queryForObject(checkSql, Integer.class, id);

                if (count != null && count > 0) {
                    // 更新记录
                    String updateSql = "UPDATE short_dramas_copy1 SET description = COALESCE(NULLIF(description, ''), ?), rating = COALESCE(rating, ?) WHERE md5_id = ?";
                    int rowsAffected = jdbcTemplate.update(updateSql, description, rating, id);
                    
                    if (rowsAffected > 0) {
                        updatedCount++;
                        log.debug("更新短剧: {} - {}", title, id);
                    }
                } else {
                    skippedCount++;
                    log.debug("跳过短剧(已有完整数据或不存在): {} - {}", title, id);
                }
            }

            log.info("数据更新完成! 更新了{}条记录，跳过了{}条记录", updatedCount, skippedCount);

        } catch (Exception e) {
            log.error("更新数据时发生错误", e);
            throw new RuntimeException("更新数据失败", e);
        }
    }

    /**
     * 读取JSON文件内容
     */
    private List<Map<String, Object>> readJsonFile() throws IOException {
        ClassPathResource resource = new ClassPathResource("test/json.json");
        try (InputStream inputStream = resource.getInputStream()) {
            TypeReference<List<Map<String, Object>>> typeRef = new TypeReference<List<Map<String, Object>>>() {};
            return objectMapper.readValue(inputStream, typeRef);
        }
    }

    /**
     * 检查short_dramas_copy1表中缺失description和rating字段的记录数量
     */
    public Map<String, Object> checkMissingData() {
        String sql = """
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN description IS NULL OR description = '' THEN 1 END) as missing_description,
                COUNT(CASE WHEN rating IS NULL THEN 1 END) as missing_rating,
                COUNT(CASE WHEN (description IS NULL OR description = '') AND rating IS NULL THEN 1 END) as missing_both
            FROM short_dramas_copy1
            """;
        
        return jdbcTemplate.queryForMap(sql);
    }

    /**
     * 获取JSON文件中的数据统计信息
     */
    public Map<String, Object> getJsonDataStats() {
        try {
            List<Map<String, Object>> dramaList = readJsonFile();
            
            long totalCount = dramaList.size();
            long withDescription = dramaList.stream()
                .filter(drama -> drama.get("description") != null && !drama.get("description").toString().trim().isEmpty())
                .count();
            long withRating = dramaList.stream()
                .filter(drama -> {
                    Map<String, Object> statistics = (Map<String, Object>) drama.get("statistics");
                    return statistics != null && statistics.get("rating") != null && !statistics.get("rating").equals(0);
                })
                .count();

            return Map.of(
                "total_records", totalCount,
                "with_description", withDescription,
                "with_rating", withRating,
                "description_percentage", withDescription * 100.0 / totalCount,
                "rating_percentage", withRating * 100.0 / totalCount
            );
        } catch (IOException e) {
            log.error("读取JSON文件统计信息失败", e);
            throw new RuntimeException("读取JSON文件失败", e);
        }
    }
}
