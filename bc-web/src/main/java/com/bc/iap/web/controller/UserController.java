package com.bc.iap.web.controller;

import com.batmobi.dataxsync.common.model.BaseResponse;
import com.bc.iap.user.model.req.UserLogReq;
import com.bc.iap.user.model.req.UserLoginReq;
import com.bc.iap.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RequiredArgsConstructor
@RestController
@RequestMapping("/api/v1/user")
@Slf4j
@Validated
public class UserController {

    private final UserService userService;

    @RequestMapping("/login")
    public BaseResponse<?> login(HttpServletRequest request, @RequestBody UserLoginReq userLoginReq) {
        return BaseResponse.success(userService.userRegisterOrLogin(request, userLoginReq));
    }

    @RequestMapping("/record")
    public BaseResponse<?> record(@RequestBody UserLogReq userLogReq) {
        userService.userLogRecord(userLogReq);
        return BaseResponse.success();
    }
}
