package com.bc.iap.web.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bc.iap.web.common.dto.EpisodesDetailReq;
import com.bc.iap.web.common.dto.EpisodesReq;
import com.bc.iap.web.common.dto.HomepageReq;
import com.bc.iap.web.common.vo.EpisodesDetailVo;
import com.bc.iap.web.common.vo.EpisodesVo;
import com.bc.iap.web.common.vo.HomepageVo;
import com.bc.iap.web.common.vo.HomepageVoCategoryName;
import com.bc.iap.web.mapper.ShortDramasMapper;
import com.bc.iap.web.service.DataUpdateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 短剧主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class IShortDramasService {

    private final ShortDramasMapper shortDramasMapper;
    private final DataUpdateService dataUpdateService;

    public HomepageVo homepage(HomepageReq req) {
        HomepageVo resp = new HomepageVo();
        resp.setSiteDomain(req.getSiteDomain());

        List<HomepageVoCategoryName> result = shortDramasMapper.homepage();
        // 按分类名称分组
        Map<String, List<HomepageVoCategoryName>> groupedByCategory = result.stream()
                .collect(Collectors.groupingBy(HomepageVoCategoryName::getName));

        // 构建Plate列表
        List<HomepageVo.Plate> plateList = groupedByCategory.entrySet().stream()
                .map(entry -> {
                    HomepageVo.Plate plate = new HomepageVo.Plate();
                    plate.setPlateName(entry.getKey()); // 分类名称作为plate名称

                    // 构建该分类下的短剧列表
                    List<HomepageVo.PlateList> dramaList = entry.getValue().stream()
                            .map(drama -> new HomepageVo.PlateList(
                                    drama.getMd5Id(),
                                    drama.getTitle(),
                                    drama.getThumbnailUrl()
                            ))
                            .collect(Collectors.toList());

                    plate.setPlateList(dramaList);
                    return plate;
                })
                .collect(Collectors.toList());

        resp.setPlateList(plateList);
        return resp;
    }

    public EpisodesVo episodesInfo(EpisodesReq req) {
        return shortDramasMapper.selectBymd5Id(req.getMd5Id());
    }

    public EpisodesDetailVo episodeDetailInfo(EpisodesDetailReq req) {
        return shortDramasMapper.selectDetailByDramaId(req.getDramaId(), req.getIndex());
    }

    /**
     * 导入参数方法 - 从JSON文件更新数据库中缺失的字段
     * 调用此方法即可开始数据导入
     */
    public void importParams() {
        log.info("开始执行数据导入...");
        try {
            dataUpdateService.updateShortDramasFromJson();
            log.info("数据导入完成");
        } catch (Exception e) {
            log.error("数据导入失败", e);
            throw new RuntimeException("数据导入失败: " + e.getMessage(), e);
        }
    }
}
