package com.bc.iap.web.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bc.iap.common.constant.ErrorCode;
import com.bc.iap.common.dto.exception.BaseException;
import com.bc.iap.user.model.dto.UserRespVo;
import com.bc.iap.user.model.resp.UserTokenResp;
import com.bc.iap.user.service.UserService;
import com.bc.iap.user.utils.UserInfoThreadLocal;
import com.bc.iap.web.common.dto.EpisodesDetailReq;
import com.bc.iap.web.common.dto.EpisodesReq;
import com.bc.iap.web.common.dto.HomepageReq;
import com.bc.iap.web.common.vo.EpisodesDetailVo;
import com.bc.iap.web.common.vo.EpisodesVo;
import com.bc.iap.web.common.vo.HomepageVo;
import com.bc.iap.web.common.vo.HomepageVoCategoryName;
import com.bc.iap.web.entity.ShortDramas;
import com.bc.iap.web.mapper.ShortDramasMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 短剧主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class IShortDramasService {

    private final ShortDramasMapper shortDramasMapper;
    private final UserService userService;

    public HomepageVo homepage(HomepageReq req) {
        if (req.getSiteDomain() == null) {
            throw new BaseException(ErrorCode.FAILURE, "siteDomain is null");
        }
        HomepageVo resp = new HomepageVo();
        resp.setSiteDomain(req.getSiteDomain());

        List<HomepageVoCategoryName> result = shortDramasMapper.homepage();
        log.info("查询到{}条短剧-分类关联记录", result.size());

        // 按分类名称分组，每个分类包含其所有短剧
        Map<String, List<HomepageVoCategoryName>> groupedByCategory = result.stream()
                .collect(Collectors.groupingBy(HomepageVoCategoryName::getName));

        log.info("共有{}个分类", groupedByCategory.size());

        // 构建Plate列表
        List<HomepageVo.Plate> plateList = groupedByCategory.entrySet().stream()
                .map(entry -> {
                    HomepageVo.Plate plate = new HomepageVo.Plate();
                    plate.setPlateName(entry.getKey()); // 分类名称作为plate名称

                    // 构建该分类下的短剧列表，去重相同的短剧
                    List<HomepageVo.PlateList> dramaList = new ArrayList<>(entry.getValue().stream()
                            .collect(Collectors.toMap(
                                    HomepageVoCategoryName::getMd5Id, // 使用md5Id作为key去重
                                    drama -> new HomepageVo.PlateList(
                                            drama.getMd5Id(),
                                            drama.getDramaId(),
                                            drama.getTitle(),
                                            drama.getViewsCount(),
                                            drama.getLikesCount(),
                                            drama.getThumbnailUrl()
                                    ),
                                    (existing, replacement) -> existing // 如果有重复，保留第一个
                            ))
                            .values());



                    plate.setPlateList(dramaList);
                    log.info("分类 {} 包含 {} 部短剧", entry.getKey(), dramaList.size());
                    return plate;
                })
                .collect(Collectors.toList());

        if (Objects.equals(req.getSiteDomain(), "bingelet.com")) {
            // 正序
            plateList.sort(Comparator.comparing(HomepageVo.Plate::getPlateName));
        }else {
            // 倒序
            plateList.sort((o1, o2) -> o2.getPlateName().compareTo(o1.getPlateName()));
        }

        resp.setPlateList(plateList);
        log.info("首页数据构建完成，共{}个分类", plateList.size());
        return resp;
    }

    public EpisodesVo episodesInfo(EpisodesReq req) {
        EpisodesVo resp = null;
        // 再查询 5 条除了 req.md5Id 以外的短剧
        LambdaQueryWrapper<ShortDramas> queryWrapper = new LambdaQueryWrapper<>();

        if (req.getMd5Id() != null && req.getDramaId() == null) {
//            先根据md5Id查询 dramaId
            queryWrapper.eq(ShortDramas::getMd5Id, req.getMd5Id());
            ShortDramas shortDramas = shortDramasMapper.selectOne(queryWrapper);
            if (shortDramas == null) {
                throw new BaseException(ErrorCode.FAILURE, "md5Id or dramaId is null");
            }
            queryWrapper.clear();
            req.setDramaId(String.valueOf(shortDramas.getDramaId()));
        }

        resp =  shortDramasMapper.selectByDramaId(Integer.valueOf(req.getDramaId()));
        queryWrapper.ne(ShortDramas::getDramaId, Integer.valueOf(req.getDramaId()));

        queryWrapper.last("limit 6");
        List<ShortDramas> mayLikeList = shortDramasMapper.selectList(queryWrapper);
        resp.setMayLikeList(mayLikeList.stream().map(drama -> new HomepageVo.PlateList(
                drama.getMd5Id(),
                drama.getDramaId(),
                drama.getTitle(),
                drama.getViewsCount(),
                drama.getLikesCount(),
                drama.getThumbnailUrl()
        )).collect(Collectors.toList()));

        return resp;
    }

    public EpisodesDetailVo episodeDetailInfo(EpisodesDetailReq req , String token) {
        log.info("查询剧集详情请求: dramaId={}", req);
        EpisodesDetailVo resp = shortDramasMapper.selectDetailByDramaId(req.getDramaId(), req.getIndex());
        log.info("剧集详情{}", resp);
        if (req.getIndex() > resp.getFreeCount() && resp.getIsVip() == 1) {
            // todo 判断是否为vip用户
            if (token == null) {
                throw new BaseException(ErrorCode.NOT_LOGIN, "not login");
            }
            UserTokenResp userTokenResp = userService.getUserBaseInfoFromToken(token);
            UserRespVo user = userService.getUser(userTokenResp.getUid());
            if (user.getUserStatus() != 2) {
                throw new BaseException(ErrorCode.USER_NOT_VIP, "not vip user");
            }
        }
        return resp;
    }
}
