package com.bc.iap.web.controller;

import com.batmobi.dataxsync.common.model.BaseResponse;
import com.bc.iap.web.service.DataUpdateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 数据更新控制器
 * 提供数据更新相关的API接口
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/data-update")
public class DataUpdateController {

    private final DataUpdateService dataUpdateService;

    /**
     * 从JSON文件更新short_dramas_copy1表中缺失的字段
     */
    @PostMapping("/update-from-json")
    public BaseResponse<String> updateFromJson() {
        try {
            log.info("开始从JSON文件更新数据库数据");
            dataUpdateService.updateShortDramasFromJson();
            return BaseResponse.success("数据更新成功");
        } catch (Exception e) {
            log.error("数据更新失败", e);
            return BaseResponse.error("数据更新失败: " + e.getMessage());
        }
    }

    /**
     * 检查数据库中缺失数据的统计信息
     */
    @GetMapping("/check-missing-data")
    public BaseResponse<Map<String, Object>> checkMissingData() {
        try {
            Map<String, Object> stats = dataUpdateService.checkMissingData();
            return BaseResponse.success(stats);
        } catch (Exception e) {
            log.error("检查缺失数据失败", e);
            return BaseResponse.error("检查缺失数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取JSON文件中的数据统计信息
     */
    @GetMapping("/json-data-stats")
    public BaseResponse<Map<String, Object>> getJsonDataStats() {
        try {
            Map<String, Object> stats = dataUpdateService.getJsonDataStats();
            return BaseResponse.success(stats);
        } catch (Exception e) {
            log.error("获取JSON数据统计失败", e);
            return BaseResponse.error("获取JSON数据统计失败: " + e.getMessage());
        }
    }
}
