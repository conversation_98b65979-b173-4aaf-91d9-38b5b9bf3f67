package com.bc.iap.common.utils;

import lombok.Builder;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

public class RequestInfoThreadLocal {


    private static final ThreadLocal<RequestInfo> threadLocal = new ThreadLocal<>();

    private static final ThreadLocal<Map<String, Object>> MAP_THREAD_LOCAL = ThreadLocal.withInitial(HashMap::new);

    public static <T> T get(Class<T> clazz, Supplier<T> supplier) {
        Object value = MAP_THREAD_LOCAL.get().computeIfAbsent(clazz.getName(), key -> supplier.get());
        return (T) value;
    }

    public static void put(Object value) {
        if( value!=null ){
            MAP_THREAD_LOCAL.get().put(value.getClass().getName(), value);
        }
    }


    public static String getIpCountry() {
        return threadLocal.get().getCountry();
    }

    public static String getIp() {
        return threadLocal.get().getIp();
    }

    public static String getAppVer() {
        return threadLocal.get().getAppVer();
    }

    public static String getAppKey() {
        return threadLocal.get().getAppKey();
    }

    public static int getVersion(){
        return threadLocal.get().getVersion();
    }


    public static void set(RequestInfo requestInfo) {
        threadLocal.set(requestInfo);
    }

    public static RequestInfo get() {
        return threadLocal.get();
    }

    public static void clear() {
        threadLocal.remove();
        MAP_THREAD_LOCAL.remove();
    }

    @Getter
    @Builder
    public static class RequestInfo {
        String ip;
        String country;
        String appVer;
        String appKey;
        /**
         * 服务端协议版本 , 从1开始,
         * 1 : 增加双币系统, 增加广告展示逻辑控制
         */
        int version;

        String channel;
        String productVersionName;
    }

}
