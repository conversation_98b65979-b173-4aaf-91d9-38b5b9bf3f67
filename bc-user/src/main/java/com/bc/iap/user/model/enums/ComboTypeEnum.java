package com.bc.iap.user.model.enums;

import lombok.Getter;

@Getter
public enum ComboTypeEnum {
    EPISODE(0, 99, "剧集"),
    WEEK(1, 7, "周卡"),
    MONTH(2, 31, "月卡"),
    SEASON(3, 90, "季卡"),
    YEAR(4, 365, "年卡"),

    ;
    private int type;
    private int day;
    private String remark;

    ComboTypeEnum(int type, int day, String remark) {
        this.type = type;
        this.day = day;
        this.remark = remark;
    }


    public static ComboTypeEnum getComboType(int comboType) {
        for (ComboTypeEnum typeEnum : ComboTypeEnum.values()) {
            if (typeEnum.getType() == comboType) {
                return typeEnum;
            }
        }
        return null;
    }
}
