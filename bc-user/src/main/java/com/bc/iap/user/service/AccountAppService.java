package com.bc.iap.user.service;

import com.bc.iap.user.model.resp.AppInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
@RequiredArgsConstructor
public class AccountAppService {

    private Map<Long, AppInfoResp> appInfoMap;

    @PostConstruct
    public void init() {
        appInfoMap = new HashMap<>();
        appInfoMap.put(7373239389677568L, new AppInfoResp().setAppKey("shore_video")
                .setAppName("shore_video").setId(7373239389677568L));
    }

    public AppInfoResp getAppInfo(Long appId) {
        return appInfoMap.get(appId);
    }
}
