package com.bc.iap.payment.constant;

/**
 * Payment constant
 * Created in 2024.07.08
 *
 * <AUTHOR>
 */
public class PaymentConstant {
    public static final String WECHAT_ROBOT_SEND_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=49d54a36-5c88-4135-90fd-5a2202e06a15";

    public final static String PAYER_MAX_PAYMENT_PATH = "/aggregate-pay/api/gateway/orderAndPay";

    public final static String PAYER_MAX_CALLBACK_PATH = "/api/v1/payment/payerMax/callback";

    public final static String PAYER_MAX_QUERY_ORDER_PATH = "/aggregate-pay/api/gateway/orderQuery";
    public final static String PAYER_MAX_SESSION_PATH = "/aggregate-pay/api/gateway/applyDropinSession";

    public final static String PAYER_MAX_SESSION_PAY_PATH = "/aggregate-pay/api/gateway/orderAndPay";

    public final static String PAYPAL_AUTH_URL = "/v1/oauth2/token";
    public final static String PAYPAL_REQ_ORDER_URL = "/v2/checkout/orders";
    public final static String PAYPAL_QUERY_ORDER_URL = "/v2/checkout/orders/%s";
    public final static String PAYPAL_CAPTURE_ORDER_URL = "/v2/checkout/orders/%s/capture";
    public final static String PAYPAL_CALLBACK_URL = "/api/v1/payment/paypal/callback";
    public final static String PAYPAL_DISPUTE_ORDERS_URL = "/v1/customer/disputes";
    public final static String PAYPAL_ORDER_DETAIL_URL = "/v1/reporting/transactions";

    public final static String PAY_SUCCESS_PATH = "/success.html";

    public final static String ORDER_SUCCESS_CODE = "00000";

    public final static String ORDER_REPEAT_CODE = "20010";

    public final static String PROXY_PAY_V1 = "web";
    public final static String PROXY_PAY_V2 = "sdk";

    public final static String FAIL_CODE = "-1";


    public interface PaypalStatus {
        String WAIT_PAY = "PAYER_ACTION_REQUIRED";
        String CREATED = "CREATED";
        String SAVED = "SAVED";
        String APPROVED = "APPROVED";
        String VOIDED = "VOIDED";
        String COMPLETED = "COMPLETED";


    }

    public interface PayerMaxStatus {
        String APPLY_SUCCESS = "APPLY_SUCCESS";
        String WAIT_PAY = "PENDING";
        String SUCCESS = "SUCCESS";
        String FAILED = "FAILED";
        String CLOSED = "CLOSED";
    }

}
