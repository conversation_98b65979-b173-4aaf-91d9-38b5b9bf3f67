package com.bc.iap.payment.enums;

import lombok.Getter;

@Getter
public enum PaymentTypeEnum {
    PAYER_MAX("payerMax", ""),
    PAYPAL("paypal", "");
    private String channel;
    private String remarks;

    PaymentTypeEnum(String channel, String remarks) {
        this.channel = channel;
        this.remarks = remarks;
    }

    public static PaymentTypeEnum getChannel(String subType) {
        for (PaymentTypeEnum channelEnum : PaymentTypeEnum.values()) {
            if (channelEnum.getChannel().equalsIgnoreCase(subType)) {
                return channelEnum;
            }
        }
        return null;
    }
}
