package com.bc.iap.payment.service.pay;

import com.alibaba.fastjson.JSON;
import com.bc.iap.common.utils.DateUtils;
import com.bc.iap.payment.config.PayerMaxProperties;
import com.bc.iap.payment.constant.PaymentConstant;
import com.bc.iap.payment.enums.CountryEnum;
import com.bc.iap.payment.enums.PaymentStatusEnum;
import com.bc.iap.payment.enums.PaymentSubTypeEnum;
import com.bc.iap.payment.enums.PaymentTypeEnum;
import com.bc.iap.payment.model.dto.PaymentCallbackInfoDto;
import com.bc.iap.payment.model.dto.PaymentInfoDto;
import com.bc.iap.payment.model.dto.PaymentOrderStatusInfoDto;
import com.bc.iap.payment.model.dto.QueryOrderStatusDto;
import com.bc.iap.payment.model.req.PayerMaxCallbackReq;
import com.bc.iap.payment.model.req.PayerMaxPayBaseReq;
import com.bc.iap.payment.model.req.PayerMaxPaymentReq;
import com.bc.iap.payment.model.req.PayerMaxQueryOrderReq;
import com.bc.iap.payment.model.resp.PayMaxPaymentResp;
import com.bc.iap.payment.model.resp.PayerMaxOrderInfoResp;
import com.bc.iap.payment.model.resp.PaymentRspDto;
import com.bc.iap.payment.service.PaymentService;
import com.bc.iap.payment.utils.RsaUtils;
import com.zz.common.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Payer max proxy pay
 * Created in 2024.07.19
 *
 * <AUTHOR>
 * @DOC https://docs.payermax.com/api.html?docName=New%20Version&docVer=v1.0&docLang=cn#/paths/aggregate-pay-api-gateway-orderAndPay/post
 */
@RequiredArgsConstructor
@Component
@Slf4j
public class PayerMaxPaymentServiceImpl implements PaymentService {

    private final PayerMaxProperties payerMaxProperties;
    private final RestTemplate restTemplate;



    @Override
    public PaymentTypeEnum getPaymentType() {
        return PaymentTypeEnum.PAYER_MAX;
    }

    /**
     * The constant countryEnums.
     */
    public static final List<CountryEnum> countryEnums = Arrays.asList(CountryEnum.values());

    @Override
    public List<CountryEnum> supportCountry() {
        return countryEnums;
    }

    @Override
    public PaymentRspDto launchPayment(PaymentInfoDto infoDto) {
        PayerMaxPaymentReq payerMaxPaymentReq = buildBaseReq(new PayerMaxPaymentReq());
        PayerMaxPaymentReq.PaymentDataDTO paymentDataDTO = new PayerMaxPaymentReq.PaymentDataDTO();
        paymentDataDTO.setOutTradeNo(infoDto.getOrderId());
        paymentDataDTO.setSubject("payment:" + infoDto.getDramaId());
        paymentDataDTO.setIntegrate("Hosted_Checkout");
        paymentDataDTO.setTotalAmount(infoDto.getAmount());
        paymentDataDTO.setCurrency(getCurrency(infoDto.getCountryEnum(), infoDto.getSubTypeEnum()));
        paymentDataDTO.setCountry(getCountry(infoDto.getCountryEnum()));
        paymentDataDTO.setUserId(String.valueOf(infoDto.getUid()));
        //携带自定义参数，回调透传
        paymentDataDTO.setReference("");
        PayerMaxPaymentReq.PaymentDetailDTO paymentDetailDTO = getPaymentDetail(infoDto);
        paymentDataDTO.setPaymentDetail(paymentDetailDTO);
        paymentDataDTO.setFrontCallbackUrl(infoDto.getSuccessUrl());
        paymentDataDTO.setNotifyUrl(payerMaxProperties.getNotifyUrl() + PaymentConstant.PAYER_MAX_CALLBACK_PATH);
        //默认1小时没支付关单
        paymentDataDTO.setExpireTime("3600");
        payerMaxPaymentReq.setData(paymentDataDTO);
        HttpHeaders headers = new HttpHeaders();
        String bodyStr = JSON.toJSONString(payerMaxPaymentReq);
        String rsaSign = RsaUtils.signForRSA(bodyStr, payerMaxProperties.getMerchantPrivateKey());
        headers.set("sign", rsaSign);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(bodyStr, headers);
        String payApiUrl = payerMaxProperties.getApiUrl() + PaymentConstant.PAYER_MAX_PAYMENT_PATH;
        ResponseEntity<PayMaxPaymentResp> resp = restTemplate.postForEntity(payApiUrl, entity, PayMaxPaymentResp.class);
        if (resp.getStatusCode() != HttpStatus.OK || resp.getBody() == null
                || !PaymentConstant.PayerMaxStatus.APPLY_SUCCESS.equals(resp.getBody().getCode())
                || !PaymentConstant.PayerMaxStatus.WAIT_PAY.equals(resp.getBody().getData().getStatus())) {
            log.warn("payerMax收款订单提交失败!result:{}", JSON.toJSONString(resp.getBody()));
            return null;
        }
        PayMaxPaymentResp payMaxPaymentResp = resp.getBody();
        PaymentRspDto paymentRspDto = new PaymentRspDto();
        paymentRspDto.setMerchantId(payerMaxProperties.getMerchantId());
        paymentRspDto.setRedirectUrl(payMaxPaymentResp.getData().getRedirectUrl());
        paymentRspDto.setOutTradeNo(payMaxPaymentResp.getData().getTradeToken());
        return paymentRspDto;
    }

    private String getCountry(CountryEnum countryEnum) {
        return countryEnum == CountryEnum.UK ? "GB" : countryEnum.getCountryCode();
    }

    private String getCurrency(CountryEnum countryEnum, PaymentSubTypeEnum subTypeEnum) {
        return (Arrays.asList(CountryEnum.BRAZIL).contains(countryEnum) && subTypeEnum == PaymentSubTypeEnum.APPLE_PAY)
                ? "USD" : countryEnum.getCurrencyCode();
    }

    private PayerMaxPaymentReq.PaymentDetailDTO getPaymentDetail(PaymentInfoDto infoDto) {
        PayerMaxPaymentReq.PaymentDetailDTO paymentDetailDTO = new PayerMaxPaymentReq.PaymentDetailDTO();
        if (infoDto.getCountryEnum() == CountryEnum.BRAZIL && infoDto.getSubTypeEnum() == PaymentSubTypeEnum.PIX) {
            paymentDetailDTO.setPaymentMethodType("WALLET");
            paymentDetailDTO.setTargetOrg("PIX");
            return paymentDetailDTO;
        }
        if (infoDto.getSubTypeEnum() == PaymentSubTypeEnum.GOOGLE_PAY) {
            paymentDetailDTO.setPaymentMethodType(PaymentSubTypeEnum.GOOGLE_PAY.getSubType());
            return paymentDetailDTO;
        }
        if (Arrays.asList(CountryEnum.DE, CountryEnum.UK).contains(infoDto.getCountryEnum()) && infoDto.getSubTypeEnum() == PaymentSubTypeEnum.KLARNA) {
            paymentDetailDTO.setPaymentMethodType("PAY_LATER");
            paymentDetailDTO.setTargetOrg("KLARNA");
            return paymentDetailDTO;
        }
        return paymentDetailDTO;
    }

    @Override
    public PaymentOrderStatusInfoDto queryOrderStatus(QueryOrderStatusDto queryOrderDto) {
        PayerMaxQueryOrderReq queryOrderReq = buildBaseReq(new PayerMaxQueryOrderReq());
        PayerMaxQueryOrderReq.DataDTO orderDataDto = new PayerMaxQueryOrderReq.DataDTO();
        orderDataDto.setOutTradeNo(queryOrderDto.getOrderId());
        queryOrderReq.setData(orderDataDto);
        HttpHeaders headers = new HttpHeaders();
        String bodyStr = JSON.toJSONString(queryOrderReq);
        String rsaSign = RsaUtils.signForRSA(bodyStr, payerMaxProperties.getMerchantPrivateKey());
        headers.set("sign", rsaSign);
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(bodyStr, headers);
        String queryUrl = payerMaxProperties.getApiUrl() + PaymentConstant.PAYER_MAX_QUERY_ORDER_PATH;
        ResponseEntity<PayerMaxOrderInfoResp> resp = restTemplate.postForEntity(queryUrl, entity, PayerMaxOrderInfoResp.class);
        if (resp.getBody() == null || resp.getBody().getData() == null) {
            log.warn("payerMax查询订单信息失败!result:{}", JSON.toJSONString(resp.getBody()));
            return null;
        }
        PayerMaxOrderInfoResp orderInfoResp = resp.getBody();
        PaymentOrderStatusInfoDto statusInfoDto = new PaymentOrderStatusInfoDto();
        statusInfoDto.setMessage(orderInfoResp.getMsg());
        statusInfoDto.setAmount(orderInfoResp.getData().getTotalAmount());
        PayerMaxOrderInfoResp.DataDTO resultDataDTO = orderInfoResp.getData();
        statusInfoDto.setStatusEnum(PaymentConstant.PayerMaxStatus.SUCCESS.equals(resultDataDTO.getStatus()) ? PaymentStatusEnum.SUCCESS_PAY :
                PaymentConstant.PayerMaxStatus.WAIT_PAY.equals(resultDataDTO.getStatus()) ? PaymentStatusEnum.WAIT_PAY :
                        PaymentConstant.PayerMaxStatus.CLOSED.equals(resultDataDTO.getStatus()) ? PaymentStatusEnum.TIMEOUT_PAY : PaymentStatusEnum.FAIL_PAY);
        if (statusInfoDto.getStatusEnum() != PaymentStatusEnum.WAIT_PAY) {
            statusInfoDto.setFinishTime(DateUtils.parseDate(resultDataDTO.getCompleteTime(), DateUtils.FMT_RFC_3339));
        }
        if (CollectionUtils.isNotEmpty(resultDataDTO.getPaymentDetails())) {
            String methodType = resultDataDTO.getPaymentDetails().get(0).getPaymentMethodType().toLowerCase();
            statusInfoDto.setPaymentMethodType("pay_later".equalsIgnoreCase(methodType) ? PaymentSubTypeEnum.KLARNA.getSubType() : methodType);
            if (resultDataDTO.getPaymentDetails().get(0).getCardInfo() != null) {
                statusInfoDto.setPaymentOtherInfo(JSON.toJSONString(resultDataDTO.getPaymentDetails().get(0).getCardInfo()));
            }
        }
        if (resultDataDTO.getFees() != null && resultDataDTO.getFees().getMerFee() != null) {
            statusInfoDto.setFee(new BigDecimal(resultDataDTO.getFees().getMerFee().getAmount()));
            statusInfoDto.setFeeCurrency(resultDataDTO.getFees().getMerFee().getCurrency());
        }
        return statusInfoDto;
    }

    public <T extends PayerMaxPayBaseReq> T buildBaseReq(T baseReq) {
        baseReq.setVersion("1.4");
        baseReq.setKeyVersion("1");
        baseReq.setRequestTime(ZonedDateTime.now().format(DateUtils.FMT_RFC_3339));
        baseReq.setAppId(payerMaxProperties.getAppId());
        baseReq.setMerchantNo(payerMaxProperties.getMerchantId());
        return baseReq;
    }

    @Override
    public PaymentCallbackInfoDto paymentCallback(Map<String, Object> paramMap, HttpServletRequest request) {
        String result = JSON.toJSONString(paramMap);
        log.info("payerMax支付回调:{}", result);
        PayerMaxCallbackReq callbackReq = JSON.parseObject(result, PayerMaxCallbackReq.class);
        if (callbackReq == null) {
            return null;
        }
        PaymentCallbackInfoDto callbackInfoDto = new PaymentCallbackInfoDto();
        PayerMaxCallbackReq.DataDTO dataDTO = callbackReq.getData();
        callbackInfoDto.setOrderId(callbackReq.getData().getOutTradeNo());
        callbackInfoDto.setMerchantId(callbackReq.getMerchantNo());
        if (PaymentConstant.PayerMaxStatus.APPLY_SUCCESS.equals(callbackReq.getCode()) &&
                PaymentConstant.PayerMaxStatus.SUCCESS.equals(dataDTO.getStatus())) {
            callbackInfoDto.setStatusEnum(PaymentStatusEnum.SUCCESS_PAY);
            callbackInfoDto.setRealAmount(dataDTO.getTotalAmount());
            if (CollectionUtils.isNotEmpty(dataDTO.getPaymentDetails())) {
                String methodType = dataDTO.getPaymentDetails().get(0).getPaymentMethodType();
                callbackInfoDto.setTransactionType("pay_later".equalsIgnoreCase(methodType) ? PaymentSubTypeEnum.KLARNA.getSubType() : methodType);
                if (dataDTO.getPaymentDetails().get(0).getCardInfo() != null) {
                    callbackInfoDto.setPaymentOtherInfo(JSON.toJSONString(dataDTO.getPaymentDetails().get(0).getCardInfo()));
                }
            }
            if (dataDTO.getFees() != null && dataDTO.getFees().getMerFee() != null) {
                callbackInfoDto.setMerFee(new BigDecimal(dataDTO.getFees().getMerFee().getAmount()));
                callbackInfoDto.setMerFeeCurrency(dataDTO.getFees().getMerFee().getCurrency());
            }
        }
        callbackInfoDto.setStatusEnum(PaymentConstant.PayerMaxStatus.SUCCESS.equals(dataDTO.getStatus()) ? PaymentStatusEnum.SUCCESS_PAY :
                PaymentConstant.PayerMaxStatus.WAIT_PAY.equals(dataDTO.getStatus()) ? PaymentStatusEnum.WAIT_PAY :
                        PaymentConstant.PayerMaxStatus.CLOSED.equals(dataDTO.getStatus()) ? PaymentStatusEnum.TIMEOUT_PAY : PaymentStatusEnum.FAIL_PAY);
        callbackInfoDto.setMessage(callbackReq.getMsg());
        Date finishTime = DateUtils.parseDate(dataDTO.getCompleteTime(), DateUtils.FMT_RFC_3339);
        callbackInfoDto.setFinishTime(finishTime);
        return callbackInfoDto;
    }
}
